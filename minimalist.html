<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist Template - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body x-data="minimalistApp()">
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" id="loading-text">Processing...</div>
        </div>
    </div>

    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1 x-text="pageTitle">Dashboard</h1>
        <div class="actions">
            <span class="material-icons" id="dark-mode-toggle" title="Toggle Dark Mode"  @click="if(navigator.vibrate) navigator.vibrate(10);">dark_mode</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Sidebar Search Input -->
        <div class="sidebar-search">
            <div class="mdc-text-field mdc-text-field--filled mdc-text-field--no-label mdc-text-field--with-leading-icon">
                <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                <input class="mdc-text-field__input"
                       type="text"
                       placeholder="Rechercher dans le menu"
                       id="sidebar-search-input">
            </div>
        </div>

        <div class="sidebar-section">
            <a href="#" class="sidebar-item" @click="setActiveNav('home')" :class="{ 'active' : activeNav === 'home'}">
                <span class="material-icons">dashboard</span>
                <span>Accueil</span>
            </a>
            <a href="#" class="sidebar-item sidebar-item-with-submenu"
            :class="{ 'active' : activeNav === 'eleves' }"
            data-submenu="eleves-submenu">
            <span class="material-icons">groups_2</span>
            <span>Élèves</span>
            <span class="material-icons expand-icon">expand_more</span>
            </a>
            <div class="submenu" id="eleves-submenu">
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('manage-eleves')">
                <span class="material-icons submenu-icon">group_add</span>
                <span>Gestion des élèves</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">format_list_numbered</span>
                <span>Attribution des classes</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">photo_camera</span>
                <span>Gestion des photos</span>
            </a>
            <a href="#" class="submenu-item" @click.prevent="setActiveNav('eleves-cards')">
                <span class="material-icons submenu-icon">event_busy</span>
                <span>Gestion des absences</span>
            </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" :class="{ 'active' : activeNav === 'section1'}" data-submenu="section1-submenu">
                <span class="material-icons">request_quote</span>
                <span>Comptabilité</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section1-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection1')">
                    <span class="material-icons submenu-icon">history</span>
                    <span>Historique des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">assessment</span>
                    <span>Rapport des paiements</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_circle</span>
                    <span>Gestion des comptables</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">school</span>
                    <span>Frais de scolarité</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection2')">
                    <span class="material-icons submenu-icon">account_balance_wallet</span>
                    <span>Solde et dépenses</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section2-submenu" :class="{ 'active' : activeNav === 'section2'}">
                <span class="material-icons">leaderboard</span>
                <span>Niveaux et Classes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section2-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">stairs</span>
                    <span>Niveaux</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">class</span>
                    <span>Classes</span>
                </a>
            </div>

            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="docs-submenu">
                <span class="material-icons">description</span>
                <span>Documents Administratifs</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="docs-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">list_alt</span>
                    <span>Listes de classe</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">calendar_month</span>
                    <span>Registres d'Appel</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">verified</span>
                    <span>Certificat de Fréquentation</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">
                    <span class="material-icons submenu-icon">folder_open</span>
                    <span>Fiches Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">badge</span>
                    <span>Cartes Scolaires</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">
                    <span class="material-icons submenu-icon">more_horiz</span>
                    <span>Divers</span>
                </a>
            </div>

            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section3-submenu" :class="{ 'active' : activeNav === 'section3'}">
                <span class="material-icons">calculate</span>
                <span>Notes et Moyennes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section3-submenu">
                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection3-submenu">
                    <span class="material-icons submenu-icon">translate</span>
                    <span>Français</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection3-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>

                <div class="submenu-item submenu-item-with-submenu" data-submenu="subsection4-submenu">
                    <span class="material-icons submenu-icon">language</span>
                    <span>Arabe</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subsection4-submenu">
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie1')">Fiches de Notation</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Notes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Moyennes</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Résultats</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Bulletins</a>
                    <a href="#" class="submenu-item" @click.prevent="setActiveNav('souspartie2')">Décisions Annuelles (DFA)</a>
                </div>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section4-submenu" :class="{ 'active' : activeNav === 'section4'}">
                <span class="material-icons">home</span>
                <span>Ecole</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section4-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">info</span>
                    <span>Infos Ecole</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection4')">
                    <span class="material-icons submenu-icon">business</span>
                    <span>Batiments et Salles</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section7-submenu" :class="{ 'active' : activeNav === 'section7'}">
                <span class="material-icons">people</span>
                <span>Ressources Humaines</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section7-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">person_add</span>
                    <span>Enseignants</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection7')">
                    <span class="material-icons submenu-icon">groups_3</span>
                    <span>Administration</span>
                </a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="section8-submenu" :class="{ 'active' : activeNav === 'section8'}">
                <span class="material-icons">school</span>
                <span>Pédagogie</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="section8-submenu">
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">date_range</span>
                    <span>Périodicité • Trimestres</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">subject</span>
                    <span>Matières</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">schedule</span>
                    <span>Emploi du temps</span>
                </a>
                <a href="#" class="submenu-item" @click.prevent="setActiveNav('subsection8')">
                    <span class="material-icons submenu-icon">assignment</span>
                    <span>Devoirs</span>
                </a>
            </div>
            <a href="#" class="sidebar-item" @click="setActiveNav('messaging')" :class="{ 'active' : activeNav === 'messaging'}">
                <span class="material-icons">mail</span>
                <span>Messagerie</span>
            </a>
            <a href="#" class="sidebar-item" @click="setActiveNav('wallet')" :class="{ 'active' : activeNav === 'wallet'}">
                <span class="material-icons">subscriptions</span>
                <span>Formation EcolePro</span>
            </a>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <h2 class="page-title">
                <span x-text="pageTitle"></span>
            </h2>
            <div class="actions">
                <!-- Page-specific actions can be added here -->
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            Content goes here
        </div>
    </div>

    <!-- Bottom App Bar (Mobile) -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-nav-item" @click="setActiveNav('home')" :class="{ 'active': activeNav === 'home' }">
            <span class="material-icons">home</span>
            <span class="bottom-nav-label">Accueil</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('page1')" :class="{ 'active': activeNav === 'page1' }">
            <span class="material-icons">people</span>
            <span class="bottom-nav-label">Page 1</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section1')" :class="{ 'active': activeNav === 'section1' }">
            <span class="material-icons">request_quote</span>
            <span class="bottom-nav-label">Section 1</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section2')" :class="{ 'active': activeNav === 'section2' }">
            <span class="material-icons">leaderboard</span>
            <span class="bottom-nav-label">Section 2</span>
        </div>
    </div>

    <!-- User Offcanvas -->
    <div class="offcanvas-overlay" id="offcanvas-overlay">
        <div class="offcanvas" id="user-offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">User Menu</div>
                <button class="offcanvas-close" id="close-user-offcanvas">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- User Profile Section -->
                <div class="offcanvas-profile">
                    <div class="profile-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name">John Doe</div>
                        <div class="profile-email">Administrator</div>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="offcanvas-menu">
                    <div class="offcanvas-menu-item">
                        <span class="material-icons">person</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Profile</div>
                            <div class="menu-item-subtitle">Manage your account</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>
                    <div class="offcanvas-menu-item">
                        <span class="material-icons">settings</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Settings</div>
                            <div class="menu-item-subtitle">App preferences</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>
                    <div class="offcanvas-divider"></div>
                    <div class="offcanvas-menu-item logout">
                        <span class="material-icons">logout</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Logout</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="overlay" id="sidebar-overlay"></div>

    <!-- Modal Component -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal" id="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Modal Title</h3>
                <button class="modal-close" id="modal-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <p>Modal content goes here...</p>
            </div>
            <div class="modal-actions" id="modal-actions">
                <button class="mdc-button" id="modal-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button class="mdc-button mdc-button--raised" id="modal-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Confirm</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Snackbar Component -->
    <div class="mdc-snackbar" id="snackbar">
        <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
            <div class="mdc-snackbar__label" id="snackbar-label" aria-atomic="false">
                Message goes here
            </div>
            <div class="mdc-snackbar__actions" id="snackbar-actions">
                <button type="button" class="mdc-button mdc-snackbar__action" id="snackbar-action">
                    <div class="mdc-button__ripple"></div>
                    <span class="mdc-button__label">Action</span>
                </button>
                <button class="mdc-icon-button mdc-snackbar__dismiss material-icons" title="Dismiss" id="snackbar-dismiss">close</button>
            </div>
        </div>
    </div>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="minimalist.js"></script>
</body>
</html>
